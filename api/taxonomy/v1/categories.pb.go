// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: taxonomy/v1/categories.proto

package taxonomyv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" validate:"required,min=1"`                
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty" validate:"required"`  
	Image         *string                `protobuf:"bytes,3,opt,name=image,proto3,oneof" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCategoryRequest) Reset() {
	*x = AddCategoryRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCategoryRequest) ProtoMessage() {}

func (x *AddCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCategoryRequest.ProtoReflect.Descriptor instead.
func (*AddCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{0}
}

func (x *AddCategoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddCategoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AddCategoryRequest) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

type AddCategoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Category              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCategoryResponse) Reset() {
	*x = AddCategoryResponse{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCategoryResponse) ProtoMessage() {}

func (x *AddCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCategoryResponse.ProtoReflect.Descriptor instead.
func (*AddCategoryResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{1}
}

func (x *AddCategoryResponse) GetData() *Category {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCategoryRequest) Reset() {
	*x = GetCategoryRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryRequest) ProtoMessage() {}

func (x *GetCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryRequest.ProtoReflect.Descriptor instead.
func (*GetCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{2}
}

func (x *GetCategoryRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetCategoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *Category              `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCategoryResponse) Reset() {
	*x = GetCategoryResponse{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCategoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryResponse) ProtoMessage() {}

func (x *GetCategoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{3}
}

func (x *GetCategoryResponse) GetData() *Category {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`                   
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" validate:"required"`                
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty" validate:"required"`  
	Image         string                 `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCategoryRequest) Reset() {
	*x = UpdateCategoryRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCategoryRequest) ProtoMessage() {}

func (x *UpdateCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCategoryRequest.ProtoReflect.Descriptor instead.
func (*UpdateCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCategoryRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCategoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateCategoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCategoryRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type DeleteCategoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCategoryRequest) Reset() {
	*x = DeleteCategoryRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCategoryRequest) ProtoMessage() {}

func (x *DeleteCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCategoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteCategoryRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteCategoryRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Category struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Slug          string                 `protobuf:"bytes,5,opt,name=slug,proto3" json:"slug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Category) Reset() {
	*x = Category{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{6}
}

func (x *Category) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Category) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Category) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Category) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Category) GetSlug() string {
	if x != nil {
		return x.Slug
	}
	return ""
}

type GetEventCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventCategoriesRequest) Reset() {
	*x = GetEventCategoriesRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventCategoriesRequest) ProtoMessage() {}

func (x *GetEventCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetEventCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{7}
}

type GetEventCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Categories    []*Category            `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEventCategoriesResponse) Reset() {
	*x = GetEventCategoriesResponse{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEventCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEventCategoriesResponse) ProtoMessage() {}

func (x *GetEventCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEventCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetEventCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{8}
}

func (x *GetEventCategoriesResponse) GetCategories() []*Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

type GetAllCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCategoriesRequest) Reset() {
	*x = GetAllCategoriesRequest{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCategoriesRequest) ProtoMessage() {}

func (x *GetAllCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetAllCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{9}
}

type GetAllCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Categories    []*Category            `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCategoriesResponse) Reset() {
	*x = GetAllCategoriesResponse{}
	mi := &file_taxonomy_v1_categories_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCategoriesResponse) ProtoMessage() {}

func (x *GetAllCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taxonomy_v1_categories_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetAllCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_taxonomy_v1_categories_proto_rawDescGZIP(), []int{10}
}

func (x *GetAllCategoriesResponse) GetCategories() []*Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_taxonomy_v1_categories_proto protoreflect.FileDescriptor

const file_taxonomy_v1_categories_proto_rawDesc = "" +
	"\n" +
	"\x1ctaxonomy/v1/categories.proto\x12\x0fapi.taxonomy.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"o\n" +
	"\x12AddCategoryRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x19\n" +
	"\x05image\x18\x03 \x01(\tH\x00R\x05image\x88\x01\x01B\b\n" +
	"\x06_image\"D\n" +
	"\x13AddCategoryResponse\x12-\n" +
	"\x04data\x18\x01 \x01(\v2\x19.api.taxonomy.v1.CategoryR\x04data\"$\n" +
	"\x12GetCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"D\n" +
	"\x13GetCategoryResponse\x12-\n" +
	"\x04data\x18\x01 \x01(\v2\x19.api.taxonomy.v1.CategoryR\x04data\"s\n" +
	"\x15UpdateCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\"'\n" +
	"\x15DeleteCategoryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x9f\x01\n" +
	"\bCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x12\n" +
	"\x04slug\x18\x05 \x01(\tR\x04slug\"\x1b\n" +
	"\x19GetEventCategoriesRequest\"W\n" +
	"\x1aGetEventCategoriesResponse\x129\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2\x19.api.taxonomy.v1.CategoryR\n" +
	"categories\"\x19\n" +
	"\x17GetAllCategoriesRequest\"U\n" +
	"\x18GetAllCategoriesResponse\x129\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2\x19.api.taxonomy.v1.CategoryR\n" +
	"categories2\xef\x04\n" +
	"\x0fCategoryService\x12b\n" +
	"\vAddCategory\x12#.api.taxonomy.v1.AddCategoryRequest\x1a$.api.taxonomy.v1.AddCategoryResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12g\n" +
	"\x10GetAllCategories\x12(.api.taxonomy.v1.GetAllCategoriesRequest\x1a).api.taxonomy.v1.GetAllCategoriesResponse\x12X\n" +
	"\vGetCategory\x12#.api.taxonomy.v1.GetCategoryRequest\x1a$.api.taxonomy.v1.GetCategoryResponse\x12b\n" +
	"\x0eUpdateCategory\x12&.api.taxonomy.v1.UpdateCategoryRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12b\n" +
	"\x0eDeleteCategory\x12&.api.taxonomy.v1.DeleteCategoryRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12m\n" +
	"\x12GetEventCategories\x12*.api.taxonomy.v1.GetEventCategoriesRequest\x1a+.api.taxonomy.v1.GetEventCategoriesResponseB6Z4github.com/nsp-inc/vtuber/api/taxonomy/v1;taxonomyv1b\x06proto3"

var (
	file_taxonomy_v1_categories_proto_rawDescOnce sync.Once
	file_taxonomy_v1_categories_proto_rawDescData []byte
)

func file_taxonomy_v1_categories_proto_rawDescGZIP() []byte {
	file_taxonomy_v1_categories_proto_rawDescOnce.Do(func() {
		file_taxonomy_v1_categories_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_taxonomy_v1_categories_proto_rawDesc), len(file_taxonomy_v1_categories_proto_rawDesc)))
	})
	return file_taxonomy_v1_categories_proto_rawDescData
}

var file_taxonomy_v1_categories_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_taxonomy_v1_categories_proto_goTypes = []any{
	(*AddCategoryRequest)(nil),         // 0: api.taxonomy.v1.AddCategoryRequest
	(*AddCategoryResponse)(nil),        // 1: api.taxonomy.v1.AddCategoryResponse
	(*GetCategoryRequest)(nil),         // 2: api.taxonomy.v1.GetCategoryRequest
	(*GetCategoryResponse)(nil),        // 3: api.taxonomy.v1.GetCategoryResponse
	(*UpdateCategoryRequest)(nil),      // 4: api.taxonomy.v1.UpdateCategoryRequest
	(*DeleteCategoryRequest)(nil),      // 5: api.taxonomy.v1.DeleteCategoryRequest
	(*Category)(nil),                   // 6: api.taxonomy.v1.Category
	(*GetEventCategoriesRequest)(nil),  // 7: api.taxonomy.v1.GetEventCategoriesRequest
	(*GetEventCategoriesResponse)(nil), // 8: api.taxonomy.v1.GetEventCategoriesResponse
	(*GetAllCategoriesRequest)(nil),    // 9: api.taxonomy.v1.GetAllCategoriesRequest
	(*GetAllCategoriesResponse)(nil),   // 10: api.taxonomy.v1.GetAllCategoriesResponse
	(*timestamppb.Timestamp)(nil),      // 11: google.protobuf.Timestamp
	(*v1.GenericResponse)(nil),         // 12: api.shared.v1.GenericResponse
}
var file_taxonomy_v1_categories_proto_depIdxs = []int32{
	6,  // 0: api.taxonomy.v1.AddCategoryResponse.data:type_name -> api.taxonomy.v1.Category
	6,  // 1: api.taxonomy.v1.GetCategoryResponse.data:type_name -> api.taxonomy.v1.Category
	11, // 2: api.taxonomy.v1.Category.created_at:type_name -> google.protobuf.Timestamp
	6,  // 3: api.taxonomy.v1.GetEventCategoriesResponse.categories:type_name -> api.taxonomy.v1.Category
	6,  // 4: api.taxonomy.v1.GetAllCategoriesResponse.categories:type_name -> api.taxonomy.v1.Category
	0,  // 5: api.taxonomy.v1.CategoryService.AddCategory:input_type -> api.taxonomy.v1.AddCategoryRequest
	9,  // 6: api.taxonomy.v1.CategoryService.GetAllCategories:input_type -> api.taxonomy.v1.GetAllCategoriesRequest
	2,  // 7: api.taxonomy.v1.CategoryService.GetCategory:input_type -> api.taxonomy.v1.GetCategoryRequest
	4,  // 8: api.taxonomy.v1.CategoryService.UpdateCategory:input_type -> api.taxonomy.v1.UpdateCategoryRequest
	5,  // 9: api.taxonomy.v1.CategoryService.DeleteCategory:input_type -> api.taxonomy.v1.DeleteCategoryRequest
	7,  // 10: api.taxonomy.v1.CategoryService.GetEventCategories:input_type -> api.taxonomy.v1.GetEventCategoriesRequest
	1,  // 11: api.taxonomy.v1.CategoryService.AddCategory:output_type -> api.taxonomy.v1.AddCategoryResponse
	10, // 12: api.taxonomy.v1.CategoryService.GetAllCategories:output_type -> api.taxonomy.v1.GetAllCategoriesResponse
	3,  // 13: api.taxonomy.v1.CategoryService.GetCategory:output_type -> api.taxonomy.v1.GetCategoryResponse
	12, // 14: api.taxonomy.v1.CategoryService.UpdateCategory:output_type -> api.shared.v1.GenericResponse
	12, // 15: api.taxonomy.v1.CategoryService.DeleteCategory:output_type -> api.shared.v1.GenericResponse
	8,  // 16: api.taxonomy.v1.CategoryService.GetEventCategories:output_type -> api.taxonomy.v1.GetEventCategoriesResponse
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_taxonomy_v1_categories_proto_init() }
func file_taxonomy_v1_categories_proto_init() {
	if File_taxonomy_v1_categories_proto != nil {
		return
	}
	file_taxonomy_v1_categories_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_taxonomy_v1_categories_proto_rawDesc), len(file_taxonomy_v1_categories_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_taxonomy_v1_categories_proto_goTypes,
		DependencyIndexes: file_taxonomy_v1_categories_proto_depIdxs,
		MessageInfos:      file_taxonomy_v1_categories_proto_msgTypes,
	}.Build()
	File_taxonomy_v1_categories_proto = out.File
	file_taxonomy_v1_categories_proto_goTypes = nil
	file_taxonomy_v1_categories_proto_depIdxs = nil
}
