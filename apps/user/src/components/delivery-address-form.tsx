import { useMutation } from "@connectrpc/connect-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { handleConnectError } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import { Card } from "@vtuber/ui/components/card";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { DateInput } from "@vtuber/ui/components/form-inputs/date-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { prefectures } from "@vtuber/ui/lib/constants";
import {
  UserDeliveryAddress,
  UserDeliveryAddressService,
} from "node_modules/@vtuber/services/src/gen/userdeliveryaddress/v1/userdeliveryaddress_pb";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const deliveryTimeOptions = [
  {
    label: "2PM~4PM（午後2時～4時）",
    value: "2PM~4PM（午後2時～4時）",
  },
  {
    label: "4PM~6PM（午後4時～6時)",
    value: "4PM~6PM（午後4時～6時)",
  },
  {
    label: "6PM~8PM（午後6時～8時）",
    value: "6PM~8PM（午後6時～8時）",
  },
  {
    label: "7PM~9PM（午後7時～9時）",
    value: "7PM~9PM（午後7時～9時）",
  },
];

export const DeliveryAddressForm = ({
  data,
}: {
  data?: UserDeliveryAddress;
}) => {
  const router = useRouter();
  const { getText } = useLanguage();
  const form = useForm({
    defaultValues: {
      addressLine1: data?.addressLine1,
      addressLine2: data?.addressLine2,
      city: data?.city,
      phoneNumber: data?.phoneNumber,
      postalCode: data?.postalCode,
      prefecture: data?.prefecture,
      recipient: data?.recipient,
      preferredDeliveryTime: data?.preferredDeliveryTime,
      preferredDeliveryDate: data?.preferredDeliveryDate,
    },
  });

  const addMutation = useMutation(
    UserDeliveryAddressService.method.addUserDeliveryAddress,
    {
      onSuccess: () => {
        toast.success("Delivery address added successfully");
        router.invalidate();
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const updateMutation = useMutation(
    UserDeliveryAddressService.method.updateUserDeliveryAddress,
    {
      onSuccess: (data) => {
        toast.success(data.message);
        router.invalidate();
      },
      onError: (err) => {
        handleConnectError(err, form);
      },
    },
  );

  const onSubmit = form.handleSubmit(async (values) => {
    if (data) {
      await updateMutation.mutateAsync({
        ...values,
        id: data?.id,
        preferredDeliveryDate: !values.preferredDeliveryDate
          ? undefined
          : values.preferredDeliveryDate,
      });
      return;
    }
    await addMutation.mutateAsync(values);
  });
  return (
    <Container className="pt-10">
      <Card className="mx-auto md:max-w-lg w-full p-8">
        <Form {...form}>
          <form
            onSubmit={onSubmit}
            className="space-y-6">
            <TextInput
              control={form.control}
              name="recipient"
              label={getText("recipient")}
              size={"lg"}
              variant={"muted"}
            />
            <TextInput
              control={form.control}
              name="phoneNumber"
              label={getText("phone_number")}
              size={"lg"}
              variant={"muted"}
              type="number"
            />
            <TextInput
              control={form.control}
              name="postalCode"
              label={getText("postal_code")}
              size={"lg"}
              variant={"muted"}
              type="number"
            />
            <SelectInput
              control={form.control}
              name="prefecture"
              options={prefectures}
              size={"lg"}
              variant={"muted"}
              label={getText("prefectures")}
            />
            <TextInput
              control={form.control}
              name="city"
              label={getText("city")}
              size={"lg"}
              variant={"muted"}
            />
            <TextInput
              control={form.control}
              name="addressLine1"
              label={getText("address_1")}
              size={"lg"}
              variant={"muted"}
            />
            <TextInput
              control={form.control}
              name="addressLine2"
              label={getText("address_2") + " (" + getText("optional") + ")"}
              size={"lg"}
              variant={"muted"}
            />
            <DateInput
              control={form.control}
              minDate={new Date()}
              name="preferredDeliveryDate"
              label={
                getText("preferred_delivery_date") +
                " (" +
                getText("optional") +
                ")"
              }
              className="bg-slate-700/50 h-[46px] px-3.5 py-2 border-slate-600 text-white placeholder-slate-400 border focus-visible:ring-1 focus-visible:ring-white rounded-md"
            />
            <SelectInput
              options={deliveryTimeOptions}
              control={form.control}
              name="preferredDeliveryTime"
              label={
                getText("preferred_delivery_time") +
                " (" +
                getText("optional") +
                ")"
              }
              size={"lg"}
              variant={"muted"}
            />
            <Button
              variant={"muted"}
              loading={addMutation.isPending || updateMutation.isPending}
              className="w-full font-semibold"
              size={"xl"}
              type="submit">
              {getText(data ? "update" : "Add")}
            </Button>
          </form>
        </Form>
      </Card>
    </Container>
  );
};
