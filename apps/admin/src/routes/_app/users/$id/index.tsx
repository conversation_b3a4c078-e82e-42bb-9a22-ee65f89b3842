import { timestampDate } from "@bufbuild/protobuf/wkt";
import { createFileRoute, notFound, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  postsClient,
  userDeliveryAddressClient,
  usersClient,
} from "@vtuber/services/client";
import { GetAllPostsResponse } from "@vtuber/services/content";
import { CampaignInvestment } from "@vtuber/services/users";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@vtuber/ui/components/card";
import { ColumnDef, DataTable } from "@vtuber/ui/components/data-table";
import { Media } from "@vtuber/ui/components/media";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import {
  Ban,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  Home,
  Mail,
  MapPin,
  Phone,
  Shield,
  TrendingUp,
  User,
} from "lucide-react";
import { useState } from "react";
import { UserPosts } from "~/components/UserPosts";
import { VtuberProfileDialog } from "~/components/VtuberDialog";
import { validatePagination } from "~/data/constatns";

export const Route = createFileRoute("/_app/users/$id/")({
  component: RouteComponent,
  validateSearch: validatePagination,
  loaderDeps: ({ search }) => search,
  loader: async ({ params, deps }) => {
    const [user] = await usersClient.getUserById({
      id: BigInt(params.id),
    });
    if (!user) {
      throw notFound({
        data: "User not found",
      });
    }
    const [userAddress] =
      await userDeliveryAddressClient.getUserDeliveryAddressById({
        id: BigInt(params.id),
      });
    const [investments] = await usersClient.getUserCampaignInvestment({
      userId: BigInt(params.id),
      pagination: {
        size: 5,
        page: deps.page,
      },
    });

    let posts: GetAllPostsResponse | null = null;
    if (user?.data?.vtuber?.id) {
      [posts] = await postsClient.getAllPosts({
        vtuberId: user?.data?.vtuber?.id,
      });
    }

    return { user, posts, investments, userAddress };
  },
});

const investmentColumsn: ColumnDef<CampaignInvestment>[] = [
  {
    accessorKey: "campaignName",
    header: "Campaign",
    cell: ({ row }) => (
      <div className=" flex items-center gap-3">
        <Avatar
          className="rounded-none"
          src={row.original.campaignThumbnail}
          fallback={row.original.campaignName}
        />
        <p>{row.original.campaignName}</p>
      </div>
    ),
  },
  {
    accessorKey: "campaignVariantTitle",
    header: "Variant",
    cell: ({ row }) => (
      <div className=" flex items-center gap-3">
        <Avatar
          className="rounded-none"
          src={row.original.campaignVariantImage}
          fallback={row.original.campaignVariantTitle}
        />
        <p>{row.original.campaignVariantTitle}</p>
      </div>
    ),
  },
  {
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => <p>¥ {row.original.amount}</p>,
  },
  {
    accessorKey: "createdAt",
    header: "Date",
    cell: ({ row }) => {
      const date = timestampDate(row.original.createdAt!);
      return <p>{date.toLocaleDateString()}</p>;
    },
  },
];

function RouteComponent() {
  const { user, posts, investments, userAddress } = Route.useLoaderData();
  const { getText } = useLanguage();
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="space-y-10">
      <Card>
        <CardHeader className="pb-0"></CardHeader>

        <CardContent className="pt-8">
          <div className="grid grid-cols-1 lg:grid-cols-[1fr_1.2fr] gap-8">
            <div className="space-y-5 p-4 bg-gradient-3 rounded-lg">
              <div className="relative flex justify-center -mt-12">
                <div className="rounded-full border-4 border-white shadow-lg">
                  <Media
                    src={user.data?.image}
                    alt={user.data?.fullName}
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>
              </div>
              <div className="text-center mb-4">
                <h1 className="text-2xl font-bold tracking-tight">
                  {user.data?.fullName}
                </h1>
                <div className="flex items-center justify-center mt-2 text-muted-foreground">
                  <Mail
                    size={16}
                    className="mr-2"
                  />
                  <span>{user.data?.email}</span>
                </div>
              </div>

              <div className="flex flex-wrap justify-center gap-2">
                {user.data?.emailVerified && (
                  <StatusBadge
                    icon={CheckCircle}
                    label="Email Verified"
                    variant="default"
                  />
                )}
                {user.data?.isVtuber && (
                  <StatusBadge
                    icon={Shield}
                    label="Vtuber"
                    variant="success"
                  />
                )}
                {user.data?.role == "user" && (
                  <StatusBadge
                    icon={User}
                    label="User"
                    variant="success"
                  />
                )}
                {user.data?.isBanned && (
                  <StatusBadge
                    icon={Ban}
                    label="Banned"
                    variant="destructive"
                  />
                )}
              </div>
            </div>

            <div className="space-y-5">
              {userAddress?.data ? (
                <Card className="shadow-sm bg-gradient-3">
                  <CardContent className="p-5">
                    <div className="flex items-center gap-2 px-1 mb-10">
                      <MapPin className="w-5 h-5 text-primary" />
                      <h2 className="text-xl font-semibold">
                        Delivery Address
                      </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-start gap-3">
                        <User className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium text-sm">
                            {userAddress?.data?.recipient}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Recipient
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Phone className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium text-sm">
                            {userAddress?.data?.phoneNumber}
                          </p>
                          <p className="text-xs text-muted-foreground">Phone</p>
                        </div>
                      </div>

                      <div className="md:col-span-2 flex items-start gap-3 pt-2">
                        <Home className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium text-sm">
                            {userAddress?.data?.addressLine1}
                          </p>
                          {userAddress?.data?.addressLine2 && (
                            <p className="text-sm">
                              {userAddress?.data?.addressLine2}
                            </p>
                          )}
                          <p className="text-sm">
                            {userAddress?.data?.city},{" "}
                            {userAddress?.data?.prefecture}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {userAddress?.data?.postalCode}
                          </p>
                        </div>
                      </div>

                      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 pt-3 border-t">
                        {userAddress?.data?.preferredDeliveryTime && (
                          <div className="flex items-start gap-3">
                            <Clock className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="font-medium text-sm">
                                {userAddress?.data?.preferredDeliveryTime}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Preferred Time
                              </p>
                            </div>
                          </div>
                        )}

                        {userAddress?.data?.preferredDeliveryDate && (
                          <div className="flex items-start gap-3">
                            <Calendar className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="font-medium text-sm">
                                {userAddress.data.preferredDeliveryDate &&
                                  timestampDate(
                                    userAddress.data.preferredDeliveryDate,
                                  ).toLocaleDateString()}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Preferred Date
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="py-8 text-center bg-muted/40">
                  <CardContent>
                    <MapPin className="w-10 h-10 mx-auto mb-3 text-muted-foreground" />
                    <p className="font-medium">No delivery address on file</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Add an address to enable shipping
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-end gap-3 border-t pt-4">
          {user.data?.isVtuber ? (
            <VtuberProfileDialog
              id={user.data?.vtuber?.id ?? BigInt(0)}
              open={open}
              setOpen={setOpen}
            />
          ) : (
            <div className="text-sm text-muted-foreground">
              {getText("No_Profile")}
            </div>
          )}
        </CardFooter>
      </Card>

      <Tabs
        defaultValue="posts"
        className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value="posts"
            className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            {getText("Recent_Posts")}
          </TabsTrigger>
          <TabsTrigger
            value="Campaigns"
            className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Campaigns
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="posts"
          className="mt-6">
          {user.data?.vtuber ? (
            posts?.data?.length ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {posts.data.map((post) => (
                  <UserPosts
                    key={post.id}
                    post={post}
                  />
                ))}
              </div>
            ) : (
              <div className="p-8 text-center border rounded-lg bg-muted/50">
                <FileText className="w-12 h-12 mx-auto mb-3 text-muted-foreground" />
                <p className="text-muted-foreground">
                  {getText("no_posts_available")}
                </p>
              </div>
            )
          ) : (
            <div className="p-8 text-center border rounded-lg bg-muted/50">
              <FileText className="w-12 h-12 mx-auto mb-3 text-muted-foreground" />
              <p className="text-muted-foreground">
                This user is not a VTuber and has no posts to display.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent
          value="Campaigns"
          className="mt-6">
          <DataTable
            onRowClick={(row) => {
              navigate({
                to: "/campaigns/$id",
                params: {
                  id: row.campaignId.toString(),
                },
              });
            }}
            columns={investmentColumsn}
            data={investments?.data || []}
            pagination={{
              currentPage: investments?.paginationDetails?.currentPage,
              totalPage: investments?.paginationDetails?.totalPages,
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

const StatusBadge = ({
  icon: Icon,
  label,
  variant,
}: {
  icon: any;
  label: string;
  variant: "default" | "success" | "destructive";
}) => {
  return (
    <Badge
      variant={variant}
      className="flex items-center gap-2 py-1">
      <Icon size={16} />
      <span>{label}</span>
    </Badge>
  );
};
