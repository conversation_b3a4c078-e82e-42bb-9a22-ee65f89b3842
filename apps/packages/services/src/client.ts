import {
  Code,
  ConnectError,
  createContextKey,
  createContextValues,
  Interceptor,
  StreamRequest,
  UnaryRequest,
} from "@connectrpc/connect";
import { createConnectTransport } from "@connectrpc/connect-web";
import { Mutex } from "async-mutex";
import { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import { BadRequestSchema } from "./gen/google/rpc/error_details_pb";

import { TransactionService } from "./gen/billing/v1/transactions_pb";
import { BillInfoService } from "./gen/billing/v1/user_billing_infos_pb";
import { CampaignVariantSubscriptionService } from "./gen/campaigns/v1/campaginvariantsubscription_pb";
import { CampaignBannerService } from "./gen/campaigns/v1/campaignbanner_pb";
import { CampaignService } from "./gen/campaigns/v1/campaigns_pb";
import { CampaignVariantService } from "./gen/campaigns/v1/campaignvariant_pb";
import { AnnouncementsService } from "./gen/cms/v1/announcements_pb";
import { FaqService } from "./gen/cms/v1/faqs_pb";
import { StaticService } from "./gen/cms/v1/static_pb";
import { PostCommentService } from "./gen/content/v1/postcomment_pb";
import { PostLikeService } from "./gen/content/v1/postlikes_pb";
import { PostService } from "./gen/content/v1/posts_pb";
import { EventCommentService } from "./gen/events/v1/eventcomment_pb";
import { EventParticipantService } from "./gen/events/v1/eventparticipant_pb";
import { EventService } from "./gen/events/v1/events_pb";
import { EventVoteService } from "./gen/events/v1/eventvote_pb";
import { NotificationsService } from "./gen/notifications/v1/notifications_pb";
import { FavoriteVtuberService } from "./gen/socials/v1/favoriitevtuber_pb";
import { FavoriteCampaignService } from "./gen/socials/v1/favoritecampaign_pb";
import { CategoryService } from "./gen/taxonomy/v1/categories_pb";
import { UserDeliveryAddressService } from "./gen/userdeliveryaddress/v1/userdeliveryaddress_pb";
import { AuthService } from "./gen/users/v1/auth_pb";
import { UserService } from "./gen/users/v1/users_pb";
import { VtuberBannerService } from "./gen/vtubers/v1/vtuberbanner_pb";
import { VtuberCategoryService } from "./gen/vtubers/v1/vtubercategory_pb";
import { VtuberGalleryService } from "./gen/vtubers/v1/vtubergallery_pb";
import { VtuberPlanService } from "./gen/vtubers/v1/vtuberplan_pb";
import { VtuberProfilesService } from "./gen/vtubers/v1/vtuberprofiles_pb";
import { VtuberUserSubscriptionService } from "./gen/vtubers/v1/vtuberusersubscription_pb";
import { createClient } from "./vtuberclient";

const baseUrl = process.env.BASE_URL;

export const uploadFile = async (
  file: File,
  signal?: AbortSignal,
  errorMessage?: string,
) => {
  const formData = new FormData();

  formData.append("file", file);
  try {
    const response = await fetch(`${baseUrl}/upload`, {
      method: "POST",
      body: formData,
      signal,
    });
    const data = await response.json();
    return data.path as string;
  } catch (error) {
    if ((error as any)?.name === "AbortError") {
      toast.error(errorMessage || "upload cancelled");
      return null;
    }
    toast.error(
      "something went wrong while uploading the file \n Please try again later",
    );
    return "";
  }
};

const mutex = new Mutex();

const noAuthContextKey = createContextKey(false);

export function NoAuthContextValues() {
  const contextValues = createContextValues();
  contextValues.set(noAuthContextKey, true);
  return contextValues;
}

const setEmptyStringtoUndefined: Interceptor = (next) => {
  return async (req: UnaryRequest | StreamRequest) => {
    if (!req.stream) {
      const message = req.message;

      // Debug logging for UpdateVtuberProfileRequest
      if (req.url.includes("UpdateVtuberProfile")) {
        console.log(
          "Interceptor - Before processing:",
          JSON.stringify(message, null, 2),
        );
        console.log("Interceptor - isPlanActive before:", message.isPlanActive);
      }

      for (const key in message) {
        // @ts-ignore
        if (typeof message[key] === "string" && message[key].length === 0) {
          // @ts-ignore
          message[key] = undefined;
        }
      }

      // Debug logging for UpdateVtuberProfileRequest
      if (req.url.includes("UpdateVtuberProfile")) {
        console.log(
          "Interceptor - After processing:",
          JSON.stringify(message, null, 2),
        );
        console.log("Interceptor - isPlanActive after:", message.isPlanActive);
      }
    }

    const res = await next(req);
    return res;
  };
};

const authInterceptor: Interceptor = (next) => {
  return async (req: UnaryRequest | StreamRequest) => {
    await mutex.waitForUnlock();
    const isNoAuth = req.contextValues.get(noAuthContextKey);
    if (!isNoAuth) {
      const token = getCookie("token");
      if (token) {
        req.header.set("Authorization", "Bearer " + token);
      }
    } else {
    }
    const res = await next(req);

    return res;
  };
};

const refreshToken = async () => {
  const transport = createConnectTransport({
    baseUrl: baseUrl!,
  });
  const authClient = createClient(AuthService, transport);
  const [tokens, err] = await authClient.refreshToken({
    refreshToken: getCookie("refresh_token"),
  });

  if (err) {
    deleteCookie("token");
    deleteCookie("refresh_token");
  }

  if (tokens) {
    setCookie("token", tokens.accessToken);
    setCookie("refresh_token", tokens.refreshToken);
    return;
  }

  throw err;
};

const RefreshTokenInterceptor: Interceptor = (next) => {
  return async (req: UnaryRequest | StreamRequest) => {
    try {
      const res = await next(req);
      return res;
    } catch (error) {
      if (error instanceof ConnectError) {
        if (
          error.rawMessage == "token expired" &&
          error.code == Code.Unauthenticated
        ) {
          if (mutex.isLocked()) {
            await mutex.waitForUnlock();
            req.header.set("Authorization", "Bearer " + getCookie("token"));
            return next(req);
          } else {
            const release = await mutex.acquire();
            try {
              await refreshToken();
              req.header.set("Authorization", "Bearer " + getCookie("token"));
              return next(req);
            } catch (error) {
              throw error;
            } finally {
              release();
            }
          }
        }
      }
      throw error;
    }
  };
};

export const transport = createConnectTransport({
  baseUrl: baseUrl!,
  interceptors: [
    setEmptyStringtoUndefined,
    authInterceptor,
    RefreshTokenInterceptor,
  ],
});

export const authClient = createClient(AuthService, transport);
export const usersClient = createClient(UserService, transport);
export const categoryClient = createClient(CategoryService, transport);
export const vtuberProfilesClient = createClient(
  VtuberProfilesService,
  transport,
);
export const vtuberCategoryClient = createClient(
  VtuberCategoryService,
  transport,
);
export const postsClient = createClient(PostService, transport);
export const postLikesClient = createClient(PostLikeService, transport);
export const postCommentClient = createClient(PostCommentService, transport);

export const eventClient = createClient(EventService, transport);
export const eventCommentClient = createClient(EventCommentService, transport);
export const faqClient = createClient(FaqService, transport);
// export const userGuideClient = createClient(UserGuideService, transport);

export const staticClient = createClient(StaticService, transport);
export const announcementClient = createClient(AnnouncementsService, transport);

export const campaignClient = createClient(CampaignService, transport);
export const campaignBannerClient = createClient(
  CampaignBannerService,
  transport,
);
export const campaignVariantClient = createClient(
  CampaignVariantService,
  transport,
);
export const campaignVariantSubscriptionClient = createClient(
  CampaignVariantSubscriptionService,
  transport,
);

export const vtuberPlanClient = createClient(VtuberPlanService, transport);
export const vtuberUserSubscriptionClient = createClient(
  VtuberUserSubscriptionService,
  transport,
);
export const transactionClient = createClient(TransactionService, transport);
// export const statClient = createClient(StatService, transport);
export const notificationClient = createClient(NotificationsService, transport);
export const billingClient = createClient(BillInfoService, transport);
export const eventParticipantClient = createClient(
  EventParticipantService,
  transport,
);
export const eventVoteServiceClient = createClient(EventVoteService, transport);
export const favoriteVtuberClient = createClient(
  FavoriteVtuberService,
  transport,
);
export const vtuberBannerClient = createClient(VtuberBannerService, transport);
export const vtuberGalleryClient = createClient(
  VtuberGalleryService,
  transport,
);
export const userDeliveryAddressClient = createClient(
  UserDeliveryAddressService,
  transport,
);
export const favoriteCampaignClient = createClient(
  FavoriteCampaignService,
  transport,
);

export function handleConnectError(
  error: ConnectError,
  form?: UseFormReturn<any, any, any>,
) {
  if (form) {
    const badRequestErrors = error.findDetails(BadRequestSchema);
    badRequestErrors.forEach((e) => {
      e.fieldViolations.forEach((fv) => {
        form.setError(fv.field, {
          message: fv.description,
        });
      });
    });
  }
  toast.error(error.rawMessage);
}

export * from "./types";
export * from "./vtuberclient";
