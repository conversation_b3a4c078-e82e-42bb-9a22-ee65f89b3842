package main

import (
	"context"
	_ "embed"
	"log/slog"
	"net/http"

	"github.com/nsp-inc/vtuber/api/userdeliveryaddress/v1/userdeliveryaddressconnect"
	userdeliveryaddressqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userdeliveryaddress"

	"connectrpc.com/connect"
	connectcors "connectrpc.com/cors"
	"github.com/go-redis/redis/v8"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/nsp-inc/vtuber/api/billing/v1/billingv1connect"
	"github.com/nsp-inc/vtuber/api/campaigns/v1/campaignsv1connect"
	"github.com/nsp-inc/vtuber/api/cms/v1/cmsv1connect"
	"github.com/nsp-inc/vtuber/api/content/v1/contentv1connect"
	"github.com/nsp-inc/vtuber/api/events/v1/eventsv1connect"
	"github.com/nsp-inc/vtuber/api/notifications/v1/notificationsv1connect"
	"github.com/nsp-inc/vtuber/api/socials/v1/socialsv1connect"
	"github.com/nsp-inc/vtuber/api/taxonomy/v1/taxonomyv1connect"
	"github.com/nsp-inc/vtuber/api/users/v1/usersv1connect"
	"github.com/nsp-inc/vtuber/api/vtubers/v1/vtubersv1connect"
	accountsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/accounts"
	announcementsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/announcements"
	campaignbannersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignbanners"
	campaigncategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigncategories"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	campaignvariantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariants"
	campaignvariantsubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariantsubscriptions"
	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	eventcategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventcategories"
	eventcommentsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventcomments"
	eventparticipantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventparticipants"
	eventparticipantvotesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventparticipantvotes"
	eventsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/events"
	faqsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/faqs"
	favoritecampaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritecampaigns"
	favoritevtubersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritevtubers"
	filesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/files"
	notificationsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/notifications"
	notificationtemplatesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/notificationtemplates"
	postcommentsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/postcomments"
	postlikesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/postlikes"
	postsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/posts"
	sessionqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/session"
	staticdataqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/staticdata"
	transactionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/transactions"
	userbillinginfosqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userbillinginfos"
	userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"
	usersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	verificationcodesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/verificationcodes"
	vtuberbannersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberbanners"
	vtubercategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtubercategories"
	vtubergalleriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtubergalleries"
	vtuberplansqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberplans"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	vtuberrequestsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberrequests"
	vtuberusersubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberusersubscriptions"
	"github.com/nsp-inc/vtuber/internal/handlers/billing"
	"github.com/nsp-inc/vtuber/internal/handlers/campaigns"
	"github.com/nsp-inc/vtuber/internal/handlers/cms"
	"github.com/nsp-inc/vtuber/internal/handlers/content"
	"github.com/nsp-inc/vtuber/internal/handlers/events"
	"github.com/nsp-inc/vtuber/internal/handlers/notifications"
	"github.com/nsp-inc/vtuber/internal/handlers/socials"
	"github.com/nsp-inc/vtuber/internal/handlers/taxonomy"
	"github.com/nsp-inc/vtuber/internal/handlers/users"
	"github.com/nsp-inc/vtuber/internal/handlers/vtubers"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/interceptors"
	"github.com/nsp-inc/vtuber/packages/logger"
	"github.com/rs/cors"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

// build

func withCORS(handler http.Handler) http.Handler {
	middleware := cors.New(cors.Options{
		AllowedOrigins: env.GetStringSlice("CORS_ALLOWED_ORIGINS", []string{"*"}),
		AllowedMethods: connectcors.AllowedMethods(),
		AllowedHeaders: append(connectcors.AllowedHeaders(), "Authorization"),
		ExposedHeaders: connectcors.ExposedHeaders(),
	})

	return middleware.Handler(handler)
}

func main() {
	ctx := context.Background()

	// Initialize logger
	logHandler := logger.NewHandler(&slog.HandlerOptions{
		Level: slog.LevelDebug,
	}, logger.Events{})

	log := slog.New(logHandler)
	slog.SetDefault(log)

	mux := http.NewServeMux()

	var redisClient = redis.NewClient(&redis.Options{
		Addr:     env.GetString("REDIS_URL", "localhost:6379"),
		Password: env.GetString("REDIS_PASSWORD", ""),
		DB:       env.GetInt("REDIS_DB", 0),
	})

	defer redisClient.Close()

	// Create DB connection
	connection, err := pgxpool.New(ctx, env.GetString("DB_STRING", "postgres://postgres:password@localhost:5432/vtuber?sslmode=disable"))

	if err != nil {
		panic(err)
	}
	withInterceptors := connect.WithInterceptors(interceptors.NewContextInterceptor(connection), interceptors.NewLoggingInterceptor(), interceptors.NewErrorInterceptor(), interceptors.NewJwtInterceptor(), interceptors.NewAuthorisationInterceptor(), interceptors.NewValidationInterceptor())

	// Init Storage Service
	storageService, err := storagev1.NewStorageService(filesqueries.New(connection))

	if err != nil {
		panic(err)
	}

	userRepo := usersqueries.New(connection)
	categoryRepo := categoriesqueries.New(connection)
	vtuberRepo := vtuberprofilesqueries.New(connection)
	postRepo := postsqueries.New(connection)
	postlikeRepo := postlikesqueries.New(connection)
	postCommentRepo := postcommentsqueries.New(connection)
	vtuberUserSubRepo := vtuberusersubscriptionsqueries.New(connection)
	userPointRepo := userpointsqueries.New(connection)
	userDeliveryAddress := userdeliveryaddressqueries.New(connection)
	staticRepo := staticdataqueries.New(connection)
	favoriteVtuberRepo := favoritevtubersqueries.New(connection)
	favoriteCampaignRepo := favoritecampaignsqueries.New(connection)
	accountsRepo := accountsqueries.New(connection)
	announcementRepo := announcementsqueries.New(connection)
	vtuberBannerRepo := vtuberbannersqueries.New(connection)
	vtuberGalleryRepo := vtubergalleriesqueries.New(connection)
	campaignCategoriesRepo := campaigncategoriesqueries.New(connection)
	sessionRepo := sessionqueries.New(connection)
	verificationCodesRepo := verificationcodesqueries.New(connection)
	eventCategoriesRepo := eventcategoriesqueries.New(connection)
	vtuberCategoriesRepo := vtubercategoriesqueries.New(connection)

	billingRepo := userbillinginfosqueries.New(connection)
	campaignVariationSubscriptionRepo := campaignvariantsubscriptionsqueries.New(connection)

	notificationTemplateRepo := notificationtemplatesqueries.New(connection)
	notificationRepo := notificationsqueries.New(connection)

	notificationService := notifications.NewNotificationService(notificationRepo, notificationTemplateRepo, vtuberRepo, userRepo, redisClient)

	userService := users.NewUserService(userRepo, storageService, userPointRepo, campaignVariationSubscriptionRepo)
	userDeliveryAddressService := users.NewDeliveryAddressService(userDeliveryAddress)
	categoryService := taxonomy.NewCategoryHandlerService(categoryRepo, storageService, eventCategoriesRepo)
	authService := users.NewAuthHandlerService(vtuberRepo, storageService, userRepo, accountsRepo, sessionRepo, verificationCodesRepo)
	profileService := vtubers.NewVtuberService(userRepo, vtuberRepo, storageService, vtuberUserSubRepo, favoriteVtuberRepo, vtuberrequestsqueries.New(connection), categoryRepo, vtuberCategoriesRepo)
	postService := content.NewPostService(postRepo, categoryRepo, vtuberRepo, storageService, vtuberUserSubRepo)
	postLikeService := content.NewPostLikeService(postlikeRepo, postRepo)
	postCommentService := content.NewPostCommentService(postCommentRepo, postRepo, notificationService, vtuberRepo)
	staticService := cms.NewStaticService(staticRepo)
	announcementService := cms.NewAnnouncementService(announcementRepo, storageService)
	vtuberBannerService := vtubers.NewVtuberBannerService(vtuberBannerRepo, storageService)
	vtuberGalleryService := vtubers.NewVtuberGalleryService(vtuberGalleryRepo, storageService)
	vtuberCategoryService := vtubers.NewVtuberCategoryHandler(vtuberCategoriesRepo)

	billingService := billing.NewUserBillingInfoHandlerService(billingRepo)

	favoriteVtuberService := socials.NewFavoriteVtuberService(favoriteVtuberRepo)

	eventRepo := eventsqueries.New(connection)
	eventCommentRepo := eventcommentsqueries.New(connection)
	eventParticipantRepo := eventparticipantsqueries.New(connection)
	eventVoteRepo := eventparticipantvotesqueries.New(connection)

	eventService := events.NewEventService(eventRepo, categoryRepo, storageService, eventCategoriesRepo)
	eventCommentService := events.NewEventCommentService(eventCommentRepo, eventRepo, notificationService, vtuberRepo)
	eventParticipantService := events.NewEventParticipantService(eventParticipantRepo, eventRepo)
	eventVoteService := events.NewEventVoteService(eventVoteRepo, eventParticipantRepo, userPointRepo)

	campaignRepo := campaignsqueries.New(connection)
	campaignBannerRepo := campaignbannersqueries.New(connection)
	campaignVariantRepo := campaignvariantsqueries.New(connection)
	transactionRepo := transactionsqueries.New(connection)
	faqsRepo := faqsqueries.New(connection)
	vtuberPlanRepo := vtuberplansqueries.New(connection)

	favoriteCampaignService := socials.NewFavoriteCampaignService(favoriteCampaignRepo, campaignRepo)
	campaignService := campaigns.NewCampaignService(campaignRepo, categoryRepo, storageService, campaignBannerRepo, campaignVariantRepo, favoriteCampaignRepo, campaignVariationSubscriptionRepo, campaignCategoriesRepo)
	campaignBannerService := campaigns.NewCampaignBannerService(campaignBannerRepo, campaignRepo, storageService)
	campaignVariantService := campaigns.NewCampaignVariantService(campaignVariantRepo, campaignRepo, transactionRepo, storageService)
	faqsService := cms.NewFAQService(faqsRepo)
	vtuberPlanService := vtubers.NewVtuberPlanService(vtuberPlanRepo)
	vtuberUserSubscriptionService := vtubers.NewVtuberUserSubscriptionService(vtuberUserSubRepo, vtuberPlanRepo, billingRepo, userPointRepo, transactionRepo, vtuberRepo)

	campaignVariationSubscriptionService := campaigns.NewCampaignVariantSubscriptionService(billingRepo, campaignVariantRepo, campaignVariationSubscriptionRepo, campaignRepo, userPointRepo, transactionRepo)

	usersPath, userHandler := usersv1connect.NewUserServiceHandler(userService, withInterceptors)
	vtuberDeliveryAddressPath, vtuberDeliveryAddressHandler := userdeliveryaddressconnect.NewUserDeliveryAddressServiceHandler(userDeliveryAddressService, withInterceptors)
	categoriesPath, categoriesHandler := taxonomyv1connect.NewCategoryServiceHandler(categoryService, withInterceptors)
	vtuberCategoryPath, vtuberCategoryHandler := vtubersv1connect.NewVtuberCategoryServiceHandler(vtuberCategoryService, withInterceptors)
	authPath, authHandler := usersv1connect.NewAuthServiceHandler(authService, withInterceptors)
	profilesPath, profilesHandler := vtubersv1connect.NewVtuberProfilesServiceHandler(profileService, withInterceptors)
	postsPath, postHandler := contentv1connect.NewPostServiceHandler(postService, withInterceptors)
	postLikesPath, postLikeHandler := contentv1connect.NewPostLikeServiceHandler(postLikeService, withInterceptors)
	postCommentPath, postCommentHandler := contentv1connect.NewPostCommentServiceHandler(postCommentService, withInterceptors)
	staticPath, staticHandler := cmsv1connect.NewStaticServiceHandler(staticService, withInterceptors)
	billingPath, billingHandler := billingv1connect.NewBillInfoServiceHandler(billingService, withInterceptors)
	favoriteVtuberPath, favoriteVtuberHandler := socialsv1connect.NewFavoriteVtuberServiceHandler(favoriteVtuberService, withInterceptors)
	favoriteCampaignPath, favoriteCampaignHandler := socialsv1connect.NewFavoriteCampaignServiceHandler(favoriteCampaignService, withInterceptors)
	announcementPath, announcementHandler := cmsv1connect.NewAnnouncementsServiceHandler(announcementService, withInterceptors)
	vtuberBannerPath, vtuberBannerHandler := vtubersv1connect.NewVtuberBannerServiceHandler(vtuberBannerService, withInterceptors)
	vtuberGalleryPath, vtuberGalleryHandler := vtubersv1connect.NewVtuberGalleryServiceHandler(vtuberGalleryService, withInterceptors)

	eventsPath, eventHandler := eventsv1connect.NewEventServiceHandler(eventService, withInterceptors)
	eventCommentPath, eventCommentHandler := eventsv1connect.NewEventCommentServiceHandler(eventCommentService, withInterceptors)
	eventParticipantPath, eventParticipantHandler := eventsv1connect.NewEventParticipantServiceHandler(eventParticipantService, withInterceptors)
	eventVotePath, eventVoteHandler := eventsv1connect.NewEventVoteServiceHandler(eventVoteService, withInterceptors)
	campaignsPath, campaignHandler := campaignsv1connect.NewCampaignServiceHandler(campaignService, withInterceptors)
	campaignBannerPath, campaignBannerHandler := campaignsv1connect.NewCampaignBannerServiceHandler(campaignBannerService, withInterceptors)
	campaignVariantPath, campaignVariantHandler := campaignsv1connect.NewCampaignVariantServiceHandler(campaignVariantService, withInterceptors)
	faqsPath, faqsHandler := cmsv1connect.NewFaqServiceHandler(faqsService, withInterceptors)
	vtuberPlanPath, vtuberPlanHandler := vtubersv1connect.NewVtuberPlanServiceHandler(vtuberPlanService, withInterceptors)
	vtuberUserSubscriptionPath, vtuberUserSubscriptionHandler := vtubersv1connect.NewVtuberUserSubscriptionServiceHandler(vtuberUserSubscriptionService, withInterceptors)

	// userGuidePath, userGuideHandler := userguidev1connect.NewUserGuideServiceHandler(userGuideService, withInterceptors)
	notificationPath, notificationHandler := notificationsv1connect.NewNotificationsServiceHandler(notificationService, withInterceptors)
	campaignVariationSubscriptionPath, campaignVariationSubscriptionHandler := campaignsv1connect.NewCampaignVariantSubscriptionServiceHandler(campaignVariationSubscriptionService, withInterceptors)

	mux.Handle(authPath, withCORS(authHandler))
	mux.Handle(vtuberBannerPath, withCORS(vtuberBannerHandler))
	mux.Handle(vtuberGalleryPath, withCORS(vtuberGalleryHandler))
	mux.Handle(usersPath, withCORS(userHandler))
	mux.Handle(categoriesPath, withCORS(categoriesHandler))
	mux.Handle(vtuberCategoryPath, withCORS(vtuberCategoryHandler))
	mux.Handle(profilesPath, withCORS(profilesHandler))
	mux.Handle(announcementPath, withCORS(announcementHandler))
	mux.Handle(postsPath, withCORS(postHandler))
	mux.Handle(postLikesPath, withCORS(postLikeHandler))
	mux.Handle(postCommentPath, withCORS(postCommentHandler))
	mux.Handle(eventsPath, withCORS(eventHandler))
	mux.Handle(eventCommentPath, withCORS(eventCommentHandler))
	mux.Handle(eventParticipantPath, withCORS(eventParticipantHandler))
	mux.Handle(eventVotePath, withCORS(eventVoteHandler))
	mux.Handle(campaignsPath, withCORS(campaignHandler))
	mux.Handle(campaignBannerPath, withCORS(campaignBannerHandler))
	mux.Handle(campaignVariantPath, withCORS(campaignVariantHandler))
	mux.Handle(faqsPath, withCORS(faqsHandler))
	mux.Handle(vtuberPlanPath, withCORS(vtuberPlanHandler))
	mux.Handle(vtuberUserSubscriptionPath, withCORS(vtuberUserSubscriptionHandler))
	mux.Handle(staticPath, withCORS(staticHandler))
	mux.Handle(vtuberDeliveryAddressPath, withCORS(vtuberDeliveryAddressHandler))
	mux.Handle(notificationPath, withCORS(notificationHandler))
	mux.Handle(billingPath, withCORS(billingHandler))
	mux.Handle(campaignVariationSubscriptionPath, withCORS(campaignVariationSubscriptionHandler))
	mux.Handle(favoriteVtuberPath, withCORS(favoriteVtuberHandler))
	mux.Handle(favoriteCampaignPath, withCORS(favoriteCampaignHandler))

	fileUploadHandler := NewFileUploadHandler(storageService)
	mux.Handle("/upload", withCORS(fileUploadHandler))

	socketService := notifications.NewWebSocketService(redisClient)

	mux.HandleFunc("/api/notifiy", func(writer http.ResponseWriter, request *http.Request) {
		redisClient.Publish(context.Background(), "user-notification-1", "hello")
		_, err := writer.Write([]byte("ok"))
		if err != nil {
			return
		}

	})

	mux.HandleFunc("/api1/ws", socketService.WebSocketHandler)

	oauthHandler := users.NewOauthHandler(userRepo, accountsRepo, vtuberRepo)

	oauthHandler.HandleOauthRoutes(mux)

	log.Info("starting server", "port", 8080, "host", "localhost", "url", "http://localhost:8080")
	err = http.ListenAndServe(":8080",
		h2c.NewHandler(mux, &http2.Server{}),
	)

	if err != nil {
		log.Error("error starting server", "error", err)
	}

}

type FileUploadHandler struct {
	storageService *storagev1.StorageService
}

func (f *FileUploadHandler) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	f.storageService.FileUploadHandler(w, req)
}

func NewFileUploadHandler(storageService *storagev1.StorageService) http.Handler {
	return &FileUploadHandler{storageService: storageService}
}
