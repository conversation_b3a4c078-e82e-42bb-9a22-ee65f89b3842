package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameVtuberProfile = "vtuber_profiles"

// VtuberProfile mapped from table <vtuber_profiles>
type VtuberProfile struct {
	ID               int64           `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID           int64           `gorm:"column:user_id;not null" json:"user_id"`
	DisplayName      string          `gorm:"column:display_name;not null" json:"display_name"`
	Furigana         string          `gorm:"column:furigana;not null" json:"furigana"`
	Image            string          `gorm:"column:image" json:"image"`
	BannerImage      string          `gorm:"column:banner_image" json:"banner_image"`
	SocialMediaLinks string          `gorm:"column:social_media_links;default:{}" json:"social_media_links"`
	Description      string          `gorm:"column:description" json:"description"`
	CreatedAt        time.Time       `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"column:deleted_at" json:"deleted_at"`
	VtuberPlans      []VtuberPlan    `gorm:"foreignKey:VtuberID;references:ID" json:"vtuber_plans"`
	Campaigns        []Campaign      `gorm:"foreignKey:VtuberID;references:ID" json:"campaigns"`
	Posts            []Post          `gorm:"foreignKey:VtuberProfileID;references:ID" json:"posts"`
	Username         string          `gorm:"column:username;not null;default:''" json:"username"`
	VtuberBanners    []VtuberBanner  `gorm:"foreignKey:VtuberID;references:ID" json:"vtuber_banners"`
	VtuberGalleries  []VtuberGallery `gorm:"foreignKey:VtuberID;references:ID" json:"vtuber_galleries"`
	Categories       []Category      `gorm:"many2many:vtuber_categories;" json:"categories"`
	IsPlanActive     bool            `gorm:"column:is_plan_active;not null;default:true" json:"is_plan_active"`
}

// TableName VtuberProfile's table name
func (*VtuberProfile) TableName() string {
	return TableNameVtuberProfile
}
