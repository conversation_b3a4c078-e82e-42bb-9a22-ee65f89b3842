package gmo

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/web"
	"golang.org/x/exp/slices"
)

type AddCardRequest struct {
	CardNumber       string `json:"card_number"`
	CardExpiryDate   string `json:"card_expiry_date"`
	CardSecurityCode string `json:"card_security_code"`
}

func AddCard(ctx context.Context, req AddCardRequest) (*string, error) {
	sessionUser := web.GetUserFromContext(ctx)
	cardInfo := map[string]string{
		"cardNo":       req.CardNumber,
		"expire":       req.CardExpiryDate,
		"securityCode": req.CardSecurityCode,
		//"holderName":   strconv.FormatInt(sessionUser.ID, 10),
		"tokenNumber": "1",
	}

	cardInfoJson, err := json.Marshal(cardInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal card info: %w", err)
	}

	buffer, err := base64.StdEncoding.DecodeString(env.GetString("PUBLIC_KEY", ""))
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 public key: %w", err)
	}
	pubKey, err := x509.ParsePKIXPublicKey(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	rsaPubKey, ok := pubKey.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}
	encryptedByte, err := rsa.EncryptPKCS1v15(rand.Reader, rsaPubKey, cardInfoJson)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt: %w", err)
	}

	encodedCiphertext := base64.StdEncoding.EncodeToString(encryptedByte)
	response, err := GetTokenFromGmo(encodedCiphertext)
	if err != nil {
		return nil, err
	}
	log.Println(response)
	if !slices.Contains(response.ResultCode, "000") {
		var index = response.ResultCode[0]
		return nil, GmoError{ErrorCode: GmoErrorCode(index)}
	}

	memberId := uuid.NewString()
	_, err = GmoAPICall(GmoApiParams{
		MemberID:   memberId,
		MemberName: sessionUser.Name,
	}, AddMemberEndPoint)
	if err != nil {
		return nil, err
	}

	//save card
	_, err = GmoAPICall(GmoApiParams{
		MemberID:    memberId,
		Token:       response.TokenObject.Token[0],
		SeqMode:     "0",
		DefaultFlag: "0",
	}, SaveCardEndPoint)
	if err != nil {
		return nil, err
	}
	return &memberId, nil
}

type SearchCardResponse struct {
	CardNumber     string `json:"card_number"`
	CardExpiryDate string `json:"card_expiry_date"`
}

func SearchCard(ctx context.Context, memberId string) (*SearchCardResponse, error) {
	call, err := GmoAPICall(GmoApiParams{
		MemberID:        memberId,
		SeqMode:         "0",
		CardSeq:         "0",
		UseFloatingMask: "0",
	}, SearchCardEndPoint)
	if err != nil {
		return nil, err
	}
	return &SearchCardResponse{
		CardNumber:     call.CardNo,
		CardExpiryDate: call.CardExpire,
	}, nil

}

func DeleteCard(ctx context.Context, memberId string) error {
	_, err := GmoAPICall(GmoApiParams{
		MemberID: memberId,
		CardSeq:  "0",
		SeqMode:  "0",
	}, DeleteCardEndPoint)
	return err
}

func GenerateGmoOrderId() string {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	timestampStr := strconv.FormatInt(timestamp, 10)
	if len(timestampStr) > 10 {
		timestampStr = timestampStr[len(timestampStr)-10:]
	}
	return timestampStr
}
